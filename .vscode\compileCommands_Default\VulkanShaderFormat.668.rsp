/FI "F:\UE_5.6\Engine\Intermediate\Build\Win64\x64\UnrealEditor\Development\VulkanShaderFormat\Definitions.VulkanShaderFormat.h" 
/FI "F:\RTSTest\Intermediate\Build\Win64\x64\RTSTestEditor\Development\CoreUObject\SharedPCH.CoreUObject.Cpp20.h" 
/I "F:\UE_5.6\Engine\Source" 
/I "F:\UE_5.6\Engine\Source\Developer\VulkanShaderFormat\Private" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\Core\Public" 
/I "F:\UE_5.6\Engine\Source\Runtime\Core\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\TraceLog\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\AutoRTFM\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\ImageCore\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\RenderCore\Public" 
/I "F:\UE_5.6\Engine\Source\Runtime\RenderCore\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\RHI\Public" 
/I "F:\UE_5.6\Engine\Source\Runtime\RHI\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VNI" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VerseVMBytecode" 
/I "F:\UE_5.6\Engine\Source\Runtime\CoreUObject\Public" 
/I "F:\UE_5.6\Engine\Source\Runtime\CoreUObject\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Public" 
/I "F:\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ShaderCompilerCommon\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ShaderCompilerCommon\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\ShaderCompilerCommon\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FileUtilities\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FileUtilities\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\FileUtilities\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ShaderPreprocessor\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ShaderPreprocessor\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\ShaderPreprocessor\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\TargetPlatform\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\TextureFormat\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\DeveloperSettings\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\AudioPlatformConfiguration\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\VNI" 
/I "F:\UE_5.6\Engine\Source\Developer\DesktopPlatform\Public" 
/I "F:\UE_5.6\Engine\Source\Developer\DesktopPlatform\Internal" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Public" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VulkanShaderFormat\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VulkanShaderFormat\VNI" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VulkanRHI\UHT" 
/I "F:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VulkanRHI\VNI" 
/I "F:\UE_5.6\Engine\Source\Runtime\VulkanRHI\Public" 
/I "F:\RTSTest\Intermediate\Build\Win64\x64\RTSTestEditor\Development\CoreUObject" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\GuidelinesSupportLibrary\GSL-1144\include" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\AtomicQueue" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\OpenGL" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\hlslcc\hlslcc\src\hlslcc_lib" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\SPIRV-Reflect\SPIRV-Reflect" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\ShaderConductor\ShaderConductor\External\SPIRV-Headers\include" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\Vulkan\Include" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\Vulkan\Include\vulkan" 
/I "F:\UE_5.6\Engine\Source\ThirdParty\Vulkan\profiles\include" 
