#include "RTSWeaponComponent.h"
#include "RTSBaseActor.h"
#include "RTSUnit.h"
#include "RTSProjectile.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "DrawDebugHelpers.h"

URTSWeaponComponent::URTSWeaponComponent()
{
    PrimaryComponentTick.bCanEverTick = true;

    // Set default rotation configuration
    BarrelSocketBaseName = TEXT("BarrelSocket");
    RotationAxis = EAxis::Y;
    MinRotation = -15.0f;
    MaxRotation = 15.0f;
    RotationSpeed = 90.0f;
    bAnimateOnFire = true;
    RecoilRotation = -10.0f;
    RecoilDuration = 0.2f;

    // Ammo will be initialized in BeginPlay based on final weapon stats
}

void URTSWeaponComponent::BeginPlay()
{
    Super::BeginPlay();

    // Initialize ammo based on magazine size
    CurrentAmmo = (WeaponStats.MagazineSize > 0) ? WeaponStats.MagazineSize : -1; // -1 means infinite ammo

    // Initialize rotations
    InitializeRotations();
}

void URTSWeaponComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    UpdateFiring(DeltaTime);
    UpdateTurretRotation(DeltaTime);
    UpdateRotationAnimations(DeltaTime);

    // Check if reload is complete
    if (bIsReloading)
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - ReloadStartTime >= WeaponStats.ReloadTime)
        {
            bIsReloading = false;
            CurrentAmmo = WeaponStats.MagazineSize;
            OnAmmoChanged.Broadcast(this, CurrentAmmo);
            OnReloadCompleted();
        }
    }
}

// Targeting Functions
void URTSWeaponComponent::SetTarget(ARTSBaseActor* Target)
{
    if (CurrentTarget.Get() != Target)
    {
        ARTSBaseActor* OldTarget = CurrentTarget.Get();
        CurrentTarget = Target;
        
        OnTargetChanged.Broadcast(this, Target);
        
        if (OldTarget)
        {
            OnTargetLost(OldTarget);
        }
        
        if (Target)
        {
            OnTargetAcquired(Target);
        }
    }
}

void URTSWeaponComponent::ClearTarget()
{
    SetTarget(nullptr);
}

bool URTSWeaponComponent::HasValidTarget() const
{
    return CurrentTarget.IsValid() && CurrentTarget->IsAlive() && CanTargetActor(CurrentTarget.Get()) && IsTargetInRange(CurrentTarget.Get());
}

bool URTSWeaponComponent::CanTargetActor(const ARTSBaseActor* Target) const
{
    if (!Target || !Target->IsAlive())
    {
        return false;
    }
    
    // Check if we're on the same team
    if (ARTSBaseActor* OwnerActor = Cast<ARTSBaseActor>(GetOwner()))
    {
        if (OwnerActor->IsOnSameTeam(Target))
        {
            return false;
        }
    }
    
    // Check targeting capabilities
    ERTSTargetingCapability Capabilities = static_cast<ERTSTargetingCapability>(TargetingCapabilities);
    
    if (const ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        switch (TargetUnit->UnitDomain)
        {
            case ERTSUnitDomain::Land:
                return EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetLand);
            case ERTSUnitDomain::Air:
                return EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetAir);
            case ERTSUnitDomain::Sea:
                return EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetSea);
            case ERTSUnitDomain::Subnautical:
                return EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetSubnautical);
            default:
                return false;
        }
    }
    
    // Buildings can be targeted if we can target land units
    if (Target->IsBuilding())
    {
        return EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetLand);
    }
    
    return true;
}

bool URTSWeaponComponent::IsTargetInRange(const ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return false;
    }
    
    float Distance = FVector::Dist(GetMuzzleLocation(), Target->GetActorLocation());
    return Distance <= WeaponStats.Range;
}



// Firing Functions
bool URTSWeaponComponent::FireAtTarget(ARTSBaseActor* Target)
{
    if (!Target || !CanFire())
    {
        return false;
    }
    
    SetTarget(Target);
    
    if (!HasValidTarget())
    {
        return false;
    }
    
    return FireAtLocation(Target->GetActorLocation());
}

bool URTSWeaponComponent::FireAtLocation(const FVector& TargetLocation)
{
    if (!CanFire())
    {
        return false;
    }
    
    // Check range
    float Distance = FVector::Dist(GetMuzzleLocation(), TargetLocation);
    if (Distance > WeaponStats.Range)
    {
        return false;
    }
    
    // Check line of sight for direct fire weapons
    if (WeaponType == ERTSWeaponType::DirectFire || WeaponType == ERTSWeaponType::Beam)
    {
        if (!HasLineOfSight(TargetLocation))
        {
            return false;
        }
    }
    
    PerformShot();
    
    // Apply accuracy
    FVector ActualTargetLocation = TargetLocation;
    if (WeaponStats.Accuracy < 1.0f)
    {
        float InaccuracyRadius = Distance * (1.0f - WeaponStats.Accuracy) * 0.1f; // 10% of distance at 0 accuracy
        FVector RandomOffset = FMath::VRand() * FMath::RandRange(0.0f, InaccuracyRadius);
        ActualTargetLocation += RandomOffset;
    }
    
    FVector FireDirection = GetFireDirection(ActualTargetLocation);

    // Handle multi-barrel firing
    if (bFireAllBarrelsSimultaneously)
    {
        // Fire all barrels at once
        for (int32 BarrelIndex = 0; BarrelIndex < NumberOfBarrels; BarrelIndex++)
        {
            SpawnProjectileFromBarrel(BarrelIndex, ActualTargetLocation, FireDirection);
        }
    }
    else
    {
        // Fire current barrel only
        SpawnProjectileFromBarrel(CurrentBarrelIndex, ActualTargetLocation, FireDirection);

        // Advance to next barrel for alternating fire
        CurrentBarrelIndex = (CurrentBarrelIndex + 1) % NumberOfBarrels;
        LastAlternatingFireTime = GetWorld()->GetTimeSeconds();
    }
    
    // Update timing and ammo
    LastFireTime = GetWorld()->GetTimeSeconds();
    
    if (WeaponStats.MagazineSize > 0)
    {
        CurrentAmmo--;
        OnAmmoChanged.Broadcast(this, CurrentAmmo);
    }
    
    // Handle burst firing
    CurrentBurstShot++;
    if (CurrentBurstShot >= WeaponStats.BurstSize)
    {
        CurrentBurstShot = 0;
    }
    
    // Broadcast events
    OnWeaponFired.Broadcast(this, GetMuzzleLocation(), ActualTargetLocation);
    OnWeaponFiredEvent(GetMuzzleLocation(), ActualTargetLocation);
    
    return true;
}

void URTSWeaponComponent::StartFiring()
{
    bIsFiring = true;
}

void URTSWeaponComponent::StopFiring()
{
    bIsFiring = false;
    CurrentBurstShot = 0;
}

bool URTSWeaponComponent::CanFire() const
{
    // Check if reloading
    if (bIsReloading)
    {
        return false;
    }

    // Check ammo
    if (!HasAmmo())
    {
        return false;
    }

    // Check rate of fire
    if (!IsReadyToFire())
    {
        return false;
    }

    return true;
}

bool URTSWeaponComponent::IsReadyToFire() const
{
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Check burst timing
    if (CurrentBurstShot > 0 && CurrentBurstShot < WeaponStats.BurstSize)
    {
        return (CurrentTime - LastBurstShotTime) >= WeaponStats.BurstDelay;
    }

    // Check alternating barrel delay
    if (!bFireAllBarrelsSimultaneously && NumberOfBarrels > 1)
    {
        if ((CurrentTime - LastAlternatingFireTime) < AlternatingBarrelDelay)
        {
            return false;
        }
    }

    // Check rate of fire
    float TimeBetweenShots = 1.0f / WeaponStats.RateOfFire;
    return (CurrentTime - LastFireTime) >= TimeBetweenShots;
}

// Ammunition Functions
void URTSWeaponComponent::Reload()
{
    if (WeaponStats.MagazineSize <= 0 || bIsReloading)
    {
        return;
    }

    bIsReloading = true;
    ReloadStartTime = GetWorld()->GetTimeSeconds();
    OnReloadStarted();
}

bool URTSWeaponComponent::NeedsReload() const
{
    return WeaponStats.MagazineSize > 0 && CurrentAmmo <= 0;
}

bool URTSWeaponComponent::HasAmmo() const
{
    return WeaponStats.MagazineSize <= 0 || CurrentAmmo > 0;
}

float URTSWeaponComponent::GetAmmoPercentage() const
{
    if (WeaponStats.MagazineSize <= 0)
    {
        return 1.0f; // Infinite ammo
    }

    return static_cast<float>(CurrentAmmo) / static_cast<float>(WeaponStats.MagazineSize);
}

// Utility Functions
FVector URTSWeaponComponent::GetMuzzleLocation() const
{
    // Use the first barrel's muzzle location as default
    return GetBarrelMuzzleLocation(0);
}

FVector URTSWeaponComponent::GetFireDirection(const FVector& TargetLocation) const
{
    if (bIsTurret && GetOwner())
    {
        // For turrets, calculate direction based on turret rotation
        FVector OwnerForward = GetOwner()->GetActorForwardVector();
        FVector OwnerRight = GetOwner()->GetActorRightVector();

        // Rotate the forward vector by the current turret rotation
        float RotationRadians = FMath::DegreesToRadians(CurrentTurretRotation);
        FVector TurretForward = OwnerForward * FMath::Cos(RotationRadians) + OwnerRight * FMath::Sin(RotationRadians);

        return TurretForward;
    }
    else
    {
        // For fixed weapons, calculate direction to target
        FVector MuzzleLocation = GetMuzzleLocation();
        return (TargetLocation - MuzzleLocation).GetSafeNormal();
    }
}

bool URTSWeaponComponent::HasLineOfSight(const FVector& TargetLocation) const
{
    if (UWorld* World = GetWorld())
    {
        FVector MuzzleLocation = GetMuzzleLocation();
        FHitResult HitResult;

        FCollisionQueryParams QueryParams;
        QueryParams.AddIgnoredActor(GetOwner());
        QueryParams.bTraceComplex = false;

        // Perform line trace
        bool bHit = World->LineTraceSingleByChannel(
            HitResult,
            MuzzleLocation,
            TargetLocation,
            ECC_Visibility,
            QueryParams
        );

        // If we hit something, check if it's close to the target
        if (bHit)
        {
            float DistanceToHit = FVector::Dist(HitResult.Location, TargetLocation);
            return DistanceToHit < 100.0f; // 1 meter tolerance
        }

        return true; // No obstruction
    }

    return false;
}

// Protected Functions
void URTSWeaponComponent::PerformShot()
{
    LastBurstShotTime = GetWorld()->GetTimeSeconds();

    // Auto-reload if needed
    if (NeedsReload())
    {
        Reload();
    }
}

void URTSWeaponComponent::SpawnProjectile(const FVector& TargetLocation, const FVector& FireDirection)
{
    // Default implementation uses the first barrel
    SpawnProjectileFromBarrel(0, TargetLocation, FireDirection);
}

void URTSWeaponComponent::SpawnProjectileFromBarrel(int32 BarrelIndex, const FVector& TargetLocation, const FVector& FireDirection)
{
    if (!ProjectileClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSWeaponComponent: No ProjectileClass set for weapon %s"), *GetName());
        return;
    }

    if (!GetWorld())
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSWeaponComponent: No World available for weapon %s"), *GetName());
        return;
    }

    // Trigger recoil animation (barrel for turrets, component for fixed weapons)
    if (bIsTurret)
    {
        TriggerRecoil(BarrelIndex);
    }
    else
    {
        TriggerRecoil(-1); // Component recoil for fixed weapons
    }

    FVector MuzzleLocation = GetBarrelMuzzleLocation(BarrelIndex);
    FRotator SpawnRotation = FireDirection.Rotation();

    // Spawn projectile
    UE_LOG(LogTemp, Log, TEXT("RTSWeaponComponent: Spawning projectile of class %s at location %s"),
        ProjectileClass ? *ProjectileClass->GetName() : TEXT("NULL"), *MuzzleLocation.ToString());

    ARTSProjectile* Projectile = GetWorld()->SpawnActor<ARTSProjectile>(
        ProjectileClass,
        MuzzleLocation,
        SpawnRotation
    );

    if (Projectile)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSWeaponComponent: Successfully spawned projectile %s"), *Projectile->GetName());
        // Configure projectile
        Projectile->Damage = WeaponStats.Damage;
        Projectile->BallisticsParams = BallisticsParams;

        // Set penetration value based on weapon type and damage
        float PenetrationValue = WeaponStats.Damage * 2.0f; // Base penetration
        ERTSDamageType DamageType = ERTSDamageType::Kinetic; // Default

        switch (WeaponType)
        {
            case ERTSWeaponType::DirectFire:
                PenetrationValue = WeaponStats.Damage * 2.5f;
                DamageType = ERTSDamageType::Kinetic;
                break;
            case ERTSWeaponType::IndirectFire:
                PenetrationValue = WeaponStats.Damage * 1.5f;
                DamageType = ERTSDamageType::Explosive;
                break;
            case ERTSWeaponType::Guided:
                PenetrationValue = WeaponStats.Damage * 3.0f;
                DamageType = ERTSDamageType::Explosive;
                break;
            case ERTSWeaponType::Beam:
                PenetrationValue = WeaponStats.Damage * 4.0f;
                DamageType = ERTSDamageType::Energy;
                break;
            case ERTSWeaponType::AntiAir:
                PenetrationValue = WeaponStats.Damage * 2.0f;
                DamageType = ERTSDamageType::Explosive;
                break;
            case ERTSWeaponType::Torpedo:
                PenetrationValue = WeaponStats.Damage * 3.5f;
                DamageType = ERTSDamageType::Explosive;
                break;
        }

        Projectile->PenetrationValue = PenetrationValue;
        Projectile->DamageType = DamageType;

        // Set projectile type based on weapon type
        switch (WeaponType)
        {
            case ERTSWeaponType::DirectFire:
                Projectile->ProjectileType = ERTSProjectileType::Ballistic;
                break;
            case ERTSWeaponType::IndirectFire:
                Projectile->ProjectileType = ERTSProjectileType::Artillery;
                break;
            case ERTSWeaponType::Guided:
                Projectile->ProjectileType = ERTSProjectileType::Guided;
                break;
            case ERTSWeaponType::Beam:
                Projectile->ProjectileType = ERTSProjectileType::Beam;
                break;
            case ERTSWeaponType::AntiAir:
                Projectile->ProjectileType = ERTSProjectileType::Missile;
                break;
            case ERTSWeaponType::Torpedo:
                Projectile->ProjectileType = ERTSProjectileType::Torpedo;
                break;
            default:
                Projectile->ProjectileType = ERTSProjectileType::Ballistic;
                break;
        }

        // Initialize projectile
        if (CurrentTarget.IsValid())
        {
            Projectile->InitializeProjectile(Cast<ARTSBaseActor>(GetOwner()), CurrentTarget.Get(), FireDirection);
        }
        else
        {
            Projectile->InitializeBallisticProjectile(Cast<ARTSBaseActor>(GetOwner()), TargetLocation, FireDirection);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSWeaponComponent: Failed to spawn projectile of class %s at location %s"),
            ProjectileClass ? *ProjectileClass->GetName() : TEXT("NULL"), *MuzzleLocation.ToString());
    }
}



void URTSWeaponComponent::UpdateFiring(float DeltaTime)
{
    if (!bIsFiring || !HasValidTarget())
    {
        return;
    }

    // Try to fire at current target
    if (CanFire())
    {
        FireAtTarget(CurrentTarget.Get());
    }
}

// Turret Functions
void URTSWeaponComponent::SetTurretRotation(float TargetRotation)
{
    if (!bIsTurret)
    {
        return;
    }

    // Clamp target rotation to limits
    TargetTurretRotation = FMath::Clamp(TargetRotation, MinRotationAngle, MaxRotationAngle);
    bIsRotatingToTarget = FMath::Abs(CurrentTurretRotation - TargetTurretRotation) > 1.0f; // 1 degree tolerance
}

bool URTSWeaponComponent::CanRotateToAngle(float TargetAngle) const
{
    if (!bIsTurret)
    {
        return true; // Non-turret weapons can always "rotate" (they're fixed)
    }

    return TargetAngle >= MinRotationAngle && TargetAngle <= MaxRotationAngle;
}

void URTSWeaponComponent::RotateTowardsTarget(const FVector& TargetLocation, float DeltaTime)
{
    if (!bIsTurret || !GetOwner())
    {
        return;
    }

    // Calculate desired rotation angle
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector OwnerForward = GetOwner()->GetActorForwardVector();
    FVector DirectionToTarget = (TargetLocation - OwnerLocation).GetSafeNormal();

    // Calculate angle relative to owner's forward direction
    float TargetAngle = FMath::RadiansToDegrees(FMath::Atan2(
        FVector::CrossProduct(OwnerForward, DirectionToTarget).Z,
        FVector::DotProduct(OwnerForward, DirectionToTarget)
    ));

    SetTurretRotation(TargetAngle);
}

bool URTSWeaponComponent::IsRotatingToTarget() const
{
    return bIsRotatingToTarget;
}

void URTSWeaponComponent::UpdateTurretRotation(float DeltaTime)
{
    if (!bIsTurret || !bIsRotatingToTarget)
    {
        return;
    }

    float RotationDifference = TargetTurretRotation - CurrentTurretRotation;

    // Check if we're close enough to the target
    if (FMath::Abs(RotationDifference) <= 1.0f)
    {
        CurrentTurretRotation = TargetTurretRotation;
        bIsRotatingToTarget = false;
        return;
    }

    // Rotate towards target
    float RotationStep = TurretRotationSpeed * DeltaTime;
    if (RotationDifference > 0.0f)
    {
        CurrentTurretRotation += FMath::Min(RotationStep, RotationDifference);
    }
    else
    {
        CurrentTurretRotation -= FMath::Min(RotationStep, -RotationDifference);
    }

    // Update the actual rotation if we have a mesh component or similar
    // This would typically involve rotating a turret mesh component
    // For now, we just track the logical rotation
}

// Multi-Barrel Functions
void URTSWeaponComponent::SetNumberOfBarrels(int32 NewBarrelCount)
{
    NumberOfBarrels = FMath::Max(1, NewBarrelCount);
    CurrentBarrelIndex = 0; // Reset to first barrel

    // Reinitialize rotations for new barrel count
    InitializeRotations();
}

void URTSWeaponComponent::SetFireAllBarrelsSimultaneously(bool bSimultaneous)
{
    bFireAllBarrelsSimultaneously = bSimultaneous;
    CurrentBarrelIndex = 0; // Reset barrel index
}

FVector URTSWeaponComponent::GetBarrelMuzzleLocation(int32 BarrelIndex) const
{
    // Clamp barrel index
    int32 ClampedIndex = FMath::Clamp(BarrelIndex, 0, NumberOfBarrels - 1);

    // Get socket name for this barrel
    FName SocketName = GetBarrelSocketName(ClampedIndex);

    // For turret weapons, use sockets with barrel rotation
    if (bIsTurret && DoesSocketExist(SocketName))
    {
        // Get socket transform and apply current barrel rotation
        FTransform SocketTransform = GetSocketTransform(SocketName, RTS_World);

        // Apply barrel rotation if we have rotation data
        if (BarrelCurrentRotations.IsValidIndex(ClampedIndex))
        {
            float CurrentRotation = BarrelCurrentRotations[ClampedIndex];
            FRotator AdditionalRotation = FRotator::ZeroRotator;

            // Apply rotation based on selected axis
            switch (RotationAxis)
            {
                case EAxis::X:
                    AdditionalRotation.Roll = CurrentRotation;
                    break;
                case EAxis::Y:
                    AdditionalRotation.Pitch = CurrentRotation;
                    break;
                case EAxis::Z:
                    AdditionalRotation.Yaw = CurrentRotation;
                    break;
            }

            SocketTransform.SetRotation(SocketTransform.GetRotation() * AdditionalRotation.Quaternion());
        }

        return SocketTransform.GetLocation();
    }

    // For fixed weapons, use component transform with component rotation and muzzle offset
    FTransform ComponentTransform = GetComponentTransform();

    // Apply component rotation for fixed weapons
    if (!bIsTurret)
    {
        FRotator AdditionalRotation = FRotator::ZeroRotator;

        // Apply rotation based on selected axis
        switch (RotationAxis)
        {
            case EAxis::X:
                AdditionalRotation.Roll = ComponentCurrentRotation;
                break;
            case EAxis::Y:
                AdditionalRotation.Pitch = ComponentCurrentRotation;
                break;
            case EAxis::Z:
                AdditionalRotation.Yaw = ComponentCurrentRotation;
                break;
        }

        ComponentTransform.SetRotation(ComponentTransform.GetRotation() * AdditionalRotation.Quaternion());
    }

    // Apply muzzle offset
    return ComponentTransform.TransformPosition(MuzzleOffset);
}

// Rotation Functions (unified for turrets and fixed weapons)
void URTSWeaponComponent::SetRotationAngle(float RotationAngle, int32 BarrelIndex)
{
    // Clamp rotation to limits
    float ClampedRotation = FMath::Clamp(RotationAngle, MinRotation, MaxRotation);

    if (bIsTurret && BarrelIndex >= 0)
    {
        // Set barrel rotation for turrets
        if (BarrelIndex < NumberOfBarrels && BarrelTargetRotations.IsValidIndex(BarrelIndex))
        {
            BarrelTargetRotations[BarrelIndex] = ClampedRotation;
        }
    }
    else
    {
        // Set component rotation for fixed weapons
        ComponentTargetRotation = ClampedRotation;
    }
}

float URTSWeaponComponent::GetRotationAngle(int32 BarrelIndex) const
{
    if (bIsTurret && BarrelIndex >= 0)
    {
        // Get barrel rotation for turrets
        if (BarrelCurrentRotations.IsValidIndex(BarrelIndex))
        {
            return BarrelCurrentRotations[BarrelIndex];
        }
    }
    else
    {
        // Get component rotation for fixed weapons
        return ComponentCurrentRotation;
    }
    return 0.0f;
}

void URTSWeaponComponent::ResetRotations()
{
    if (bIsTurret)
    {
        // Reset all barrel rotations for turrets
        for (int32 i = 0; i < NumberOfBarrels; i++)
        {
            SetRotationAngle(0.0f, i);
        }
    }
    else
    {
        // Reset component rotation for fixed weapons
        SetRotationAngle(0.0f, -1);
    }
}

bool URTSWeaponComponent::HasBarrelSocket(int32 BarrelIndex) const
{
    FName SocketName = GetBarrelSocketName(BarrelIndex);
    return DoesSocketExist(SocketName);
}

// Rotation Animation System Implementation
void URTSWeaponComponent::InitializeRotations()
{
    if (bIsTurret)
    {
        // Initialize barrel rotations for turrets
        BarrelCurrentRotations.SetNum(NumberOfBarrels);
        BarrelTargetRotations.SetNum(NumberOfBarrels);
        BarrelRecoilStartTimes.SetNum(NumberOfBarrels);

        for (int32 i = 0; i < NumberOfBarrels; i++)
        {
            BarrelCurrentRotations[i] = 0.0f;
            BarrelTargetRotations[i] = 0.0f;
            BarrelRecoilStartTimes[i] = -1.0f; // -1 means not in recoil
        }
    }
    else
    {
        // Initialize component rotation for fixed weapons
        ComponentCurrentRotation = 0.0f;
        ComponentTargetRotation = 0.0f;
        ComponentRecoilStartTime = -1.0f;
    }
}

void URTSWeaponComponent::UpdateRotationAnimations(float DeltaTime)
{
    if (!bAnimateOnFire)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    if (bIsTurret)
    {
        // Update barrel rotations for turrets
        for (int32 i = 0; i < NumberOfBarrels; i++)
        {
            if (!BarrelCurrentRotations.IsValidIndex(i) || !BarrelTargetRotations.IsValidIndex(i))
            {
                continue;
            }

            // Check if barrel is in recoil
            if (BarrelRecoilStartTimes.IsValidIndex(i) && BarrelRecoilStartTimes[i] >= 0.0f)
            {
                float RecoilElapsed = CurrentTime - BarrelRecoilStartTimes[i];

                if (RecoilElapsed >= RecoilDuration)
                {
                    // Recoil finished, return to neutral rotation
                    BarrelRecoilStartTimes[i] = -1.0f;
                    BarrelTargetRotations[i] = 0.0f;
                }
            }

            // Animate towards target rotation
            float CurrentRot = BarrelCurrentRotations[i];
            float TargetRot = BarrelTargetRotations[i];

            if (!FMath::IsNearlyEqual(CurrentRot, TargetRot, 0.1f))
            {
                float Direction = FMath::Sign(TargetRot - CurrentRot);
                float Movement = RotationSpeed * DeltaTime * Direction;

                // Don't overshoot the target
                if (FMath::Abs(Movement) > FMath::Abs(TargetRot - CurrentRot))
                {
                    CurrentRot = TargetRot;
                }
                else
                {
                    CurrentRot += Movement;
                }

                // Clamp to limits
                CurrentRot = FMath::Clamp(CurrentRot, MinRotation, MaxRotation);
                BarrelCurrentRotations[i] = CurrentRot;

                // Apply the rotation to the socket
                SetBarrelRotation(i, CurrentRot);
            }
        }
    }
    else
    {
        // Update component rotation for fixed weapons
        // Check if component is in recoil
        if (ComponentRecoilStartTime >= 0.0f)
        {
            float RecoilElapsed = CurrentTime - ComponentRecoilStartTime;

            if (RecoilElapsed >= RecoilDuration)
            {
                // Recoil finished, return to neutral rotation
                ComponentRecoilStartTime = -1.0f;
                ComponentTargetRotation = 0.0f;
            }
        }

        // Animate towards target rotation
        float CurrentRot = ComponentCurrentRotation;
        float TargetRot = ComponentTargetRotation;

        if (!FMath::IsNearlyEqual(CurrentRot, TargetRot, 0.1f))
        {
            float Direction = FMath::Sign(TargetRot - CurrentRot);
            float Movement = RotationSpeed * DeltaTime * Direction;

            // Don't overshoot the target
            if (FMath::Abs(Movement) > FMath::Abs(TargetRot - CurrentRot))
            {
                CurrentRot = TargetRot;
            }
            else
            {
                CurrentRot += Movement;
            }

            // Clamp to limits
            CurrentRot = FMath::Clamp(CurrentRot, MinRotation, MaxRotation);
            ComponentCurrentRotation = CurrentRot;

            // Apply the rotation to the component
            SetComponentRotation(CurrentRot);
        }
    }
}

void URTSWeaponComponent::TriggerRecoil(int32 BarrelIndex)
{
    if (!bAnimateOnFire)
    {
        return;
    }

    if (bIsTurret && BarrelIndex >= 0)
    {
        // Trigger barrel recoil for turrets
        if (BarrelIndex < NumberOfBarrels && BarrelRecoilStartTimes.IsValidIndex(BarrelIndex) && BarrelTargetRotations.IsValidIndex(BarrelIndex))
        {
            BarrelRecoilStartTimes[BarrelIndex] = GetWorld()->GetTimeSeconds();
            BarrelTargetRotations[BarrelIndex] = RecoilRotation;
        }
    }
    else
    {
        // Trigger component recoil for fixed weapons
        ComponentRecoilStartTime = GetWorld()->GetTimeSeconds();
        ComponentTargetRotation = RecoilRotation;
    }
}

void URTSWeaponComponent::SetBarrelRotation(int32 BarrelIndex, float Rotation)
{
    // Note: UE5's StaticMeshComponent doesn't have a direct way to modify socket rotations at runtime
    // This would typically require a custom implementation or using a SkeletalMeshComponent
    // For now, we'll store the rotation and use it in GetBarrelMuzzleLocation
    // The rotation is applied when calculating the muzzle location
}

void URTSWeaponComponent::SetComponentRotation(float Rotation)
{
    // Apply rotation to the component itself
    FRotator CurrentRotation = GetComponentRotation();
    FRotator NewRotation = CurrentRotation;

    // Apply rotation based on selected axis
    switch (RotationAxis)
    {
        case EAxis::X:
            NewRotation.Roll = CurrentRotation.Roll + Rotation;
            break;
        case EAxis::Y:
            NewRotation.Pitch = CurrentRotation.Pitch + Rotation;
            break;
        case EAxis::Z:
            NewRotation.Yaw = CurrentRotation.Yaw + Rotation;
            break;
    }

    SetWorldRotation(NewRotation);
}

FName URTSWeaponComponent::GetBarrelSocketName(int32 BarrelIndex) const
{
    if (NumberOfBarrels <= 1)
    {
        return FName(*BarrelSocketBaseName);
    }

    // For multiple barrels, append the index: BarrelSocket_0, BarrelSocket_1, etc.
    return FName(*FString::Printf(TEXT("%s_%d"), *BarrelSocketBaseName, BarrelIndex));
}

UStaticMeshComponent* URTSWeaponComponent::GetTurretMeshComponent() const
{
    if (!bIsTurret || TurretMeshComponentName.IsEmpty())
    {
        return nullptr;
    }

    // Find the turret mesh component by name in the owner actor
    if (AActor* Owner = GetOwner())
    {
        // First try to find by exact component name
        TArray<UStaticMeshComponent*> MeshComponents;
        Owner->GetComponents<UStaticMeshComponent>(MeshComponents);

        for (UStaticMeshComponent* MeshComp : MeshComponents)
        {
            if (MeshComp && MeshComp->GetName() == TurretMeshComponentName)
            {
                return MeshComp;
            }
        }

        // If not found by exact name, try to find a child component of this weapon component
        TArray<USceneComponent*> ChildComponents;
        GetChildrenComponents(true, ChildComponents);

        for (USceneComponent* Child : ChildComponents)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Child))
            {
                if (MeshComp->GetName().Contains(TurretMeshComponentName))
                {
                    return MeshComp;
                }
            }
        }
    }

    return nullptr;
}
