#pragma once

#include "CoreMinimal.h"
#include "Components/StaticMeshComponent.h"
#include "RTSProjectile.h"
#include "RTSBaseActor.h"
#include "RTSWeaponComponent.generated.h"

// Enum for targeting capabilities - what types of units this weapon can attack
UENUM(BlueprintType, meta = (Bitflags, UseEnumValuesAsMaskValuesInEditor = "true"))
enum class ERTSTargetingCapability : uint8
{
    None            = 0         UMETA(DisplayName = "None"),
    CanTargetLand   = 1 << 0    UMETA(DisplayName = "Can Target Land"),
    CanTargetAir    = 1 << 1    UMETA(DisplayName = "Can Target Air"),
    CanTargetSea    = 1 << 2    UMETA(DisplayName = "Can Target Sea"),
    CanTargetSubnautical = 1 << 3 UMETA(DisplayName = "Can Target Subnautical"),

    // Convenience combinations
    CanTargetSurface = CanTargetLand | CanTargetSea UMETA(DisplayName = "Can Target Surface (Land + Sea)"),
    CanTargetAll = CanTargetLand | CanTargetAir | CanTargetSea | CanTargetSubnautical UMETA(DisplayName = "Can Target All")
};
ENUM_CLASS_FLAGS(ERTSTargetingCapability);

// Enum for weapon types
UENUM(BlueprintType)
enum class ERTSWeaponType : uint8
{
    DirectFire      UMETA(DisplayName = "Direct Fire"),        // Line of sight weapons
    IndirectFire    UMETA(DisplayName = "Indirect Fire"),     // Artillery, mortars
    Guided          UMETA(DisplayName = "Guided"),            // Missiles, guided rockets
    Beam            UMETA(DisplayName = "Beam"),              // Lasers, particle beams
    Melee           UMETA(DisplayName = "Melee"),             // Close combat
    AntiAir         UMETA(DisplayName = "Anti-Air"),          // Specialized AA weapons
    Torpedo         UMETA(DisplayName = "Torpedo")            // Underwater weapons
};

// Struct for weapon statistics
USTRUCT(BlueprintType)
struct ARMORWARS_API FWeaponStats
{
    GENERATED_BODY()

    // Damage per shot
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Damage = 25.0f;

    // Rate of fire (shots per second)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float RateOfFire = 1.0f;

    // Maximum range
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Range = 800.0f;

    // Accuracy (0.0 = always miss, 1.0 = perfect accuracy)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Accuracy = 0.9f;

    // Reload time (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float ReloadTime = 2.0f;

    // Magazine size (0 = infinite)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 MagazineSize = 0;

    // Burst size (shots per burst)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 BurstSize = 1;

    // Time between shots in a burst
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float BurstDelay = 0.1f;

    FWeaponStats() = default;
};

/**
 * Weapon component for RTS units and buildings
 * Handles targeting, firing, and projectile spawning with realistic ballistics
 * Now uses StaticMeshComponent for visual representation and socket-based barrel positioning
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSWeaponComponent : public UStaticMeshComponent
{
    GENERATED_BODY()

public:
    URTSWeaponComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
    // Weapon configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
    ERTSWeaponType WeaponType = ERTSWeaponType::DirectFire;

    // Weapon statistics
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
    FWeaponStats WeaponStats;

    // Projectile class to spawn
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
    TSubclassOf<ARTSProjectile> ProjectileClass;

    // Ballistics parameters for projectiles
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    FBallisticsParameters BallisticsParams;

    // What types of units this weapon can target
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting", meta = (Bitmask, BitmaskEnum = "ERTSTargetingCapability"))
    int32 TargetingCapabilities = static_cast<int32>(ERTSTargetingCapability::CanTargetAll);

    // Muzzle location offset from actor center
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
    FVector MuzzleOffset = FVector(100.0f, 0.0f, 0.0f);



    // Turret configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret")
    bool bIsTurret = false;

    // Turret mesh component name (child component that contains barrel sockets and rotates)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret", meta = (EditCondition = "bIsTurret"))
    FString TurretMeshComponentName = TEXT("TurretMesh");

    // Turret rotation speed (degrees per second)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret", meta = (EditCondition = "bIsTurret", ClampMin = "0.0"))
    float TurretRotationSpeed = 90.0f;

    // Minimum rotation angle (degrees, relative to forward)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret", meta = (EditCondition = "bIsTurret", ClampMin = "-180.0", ClampMax = "180.0"))
    float MinRotationAngle = -180.0f;

    // Maximum rotation angle (degrees, relative to forward)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret", meta = (EditCondition = "bIsTurret", ClampMin = "-180.0", ClampMax = "180.0"))
    float MaxRotationAngle = 180.0f;

    // Current turret rotation (degrees, relative to forward)
    UPROPERTY(BlueprintReadOnly, Category = "Turret")
    float CurrentTurretRotation = 0.0f;

    // Multi-barrel configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Barrel", meta = (ClampMin = "1"))
    int32 NumberOfBarrels = 1;

    // Whether to fire all barrels simultaneously or alternating
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Barrel")
    bool bFireAllBarrelsSimultaneously = true;

    // Delay between barrel fires when alternating (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Barrel", meta = (EditCondition = "!bFireAllBarrelsSimultaneously", ClampMin = "0.0"))
    float AlternatingBarrelDelay = 0.1f;

    // Current barrel index for alternating fire
    UPROPERTY(BlueprintReadOnly, Category = "Multi-Barrel")
    int32 CurrentBarrelIndex = 0;

    // Barrel Socket Configuration (only used for turret weapons)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Barrel Sockets", meta = (EditCondition = "bIsTurret"))
    FString BarrelSocketBaseName = TEXT("BarrelSocket");

    // Which axis to rotate the barrels around (for turrets) or component around (for fixed weapons)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation")
    TEnumAsByte<EAxis::Type> RotationAxis = EAxis::Y;

    // Minimum rotation for barrels/component (in degrees)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation")
    float MinRotation = -15.0f;

    // Maximum rotation for barrels/component (in degrees)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation")
    float MaxRotation = 15.0f;

    // Speed of rotation (degrees per second)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation", meta = (ClampMin = "0.0"))
    float RotationSpeed = 90.0f;

    // Whether to animate when firing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation")
    bool bAnimateOnFire = true;

    // How much to rotate when firing (in degrees)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation", meta = (EditCondition = "bAnimateOnFire"))
    float RecoilRotation = -10.0f;

    // How long to stay in recoil rotation (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation", meta = (EditCondition = "bAnimateOnFire", ClampMin = "0.0"))
    float RecoilDuration = 0.2f;

    // Current target
    UPROPERTY(BlueprintReadOnly, Category = "Targeting")
    TWeakObjectPtr<ARTSBaseActor> CurrentTarget;

    // Current ammunition count
    UPROPERTY(BlueprintReadOnly, Category = "Weapon")
    int32 CurrentAmmo = 0;

    // Whether the weapon is currently reloading
    UPROPERTY(BlueprintReadOnly, Category = "Weapon")
    bool bIsReloading = false;

    // Whether the weapon is currently firing
    UPROPERTY(BlueprintReadOnly, Category = "Weapon")
    bool bIsFiring = false;

public:
    // Targeting functions
    UFUNCTION(BlueprintCallable, Category = "Weapon|Targeting")
    void SetTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Weapon|Targeting")
    void ClearTarget();

    UFUNCTION(BlueprintPure, Category = "Weapon|Targeting")
    bool HasValidTarget() const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Targeting")
    bool CanTargetActor(const ARTSBaseActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Targeting")
    bool IsTargetInRange(const ARTSBaseActor* Target) const;



    // Firing functions
    UFUNCTION(BlueprintCallable, Category = "Weapon|Firing")
    bool FireAtTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Weapon|Firing")
    bool FireAtLocation(const FVector& TargetLocation);

    UFUNCTION(BlueprintCallable, Category = "Weapon|Firing")
    void StartFiring();

    UFUNCTION(BlueprintCallable, Category = "Weapon|Firing")
    void StopFiring();

    UFUNCTION(BlueprintPure, Category = "Weapon|Firing")
    bool CanFire() const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Firing")
    bool IsReadyToFire() const;

    // Ammunition functions
    UFUNCTION(BlueprintCallable, Category = "Weapon|Ammo")
    void Reload();

    UFUNCTION(BlueprintPure, Category = "Weapon|Ammo")
    bool NeedsReload() const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Ammo")
    bool HasAmmo() const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Ammo")
    float GetAmmoPercentage() const;

    // Utility functions
    UFUNCTION(BlueprintPure, Category = "Weapon")
    FVector GetMuzzleLocation() const;

    UFUNCTION(BlueprintPure, Category = "Weapon")
    FVector GetFireDirection(const FVector& TargetLocation) const;

    UFUNCTION(BlueprintPure, Category = "Weapon")
    bool HasLineOfSight(const FVector& TargetLocation) const;

    // Turret functions
    UFUNCTION(BlueprintCallable, Category = "Weapon|Turret")
    void SetTurretRotation(float TargetRotation);

    UFUNCTION(BlueprintPure, Category = "Weapon|Turret")
    bool CanRotateToAngle(float TargetAngle) const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Turret")
    float GetTurretRotation() const { return CurrentTurretRotation; }

    UFUNCTION(BlueprintPure, Category = "Weapon|Turret")
    bool IsTurret() const { return bIsTurret; }

    UFUNCTION(BlueprintCallable, Category = "Weapon|Turret")
    void RotateTowardsTarget(const FVector& TargetLocation, float DeltaTime);

    UFUNCTION(BlueprintPure, Category = "Weapon|Turret")
    bool IsRotatingToTarget() const;

    // Multi-barrel functions
    UFUNCTION(BlueprintPure, Category = "Weapon|Multi-Barrel")
    int32 GetNumberOfBarrels() const { return NumberOfBarrels; }

    UFUNCTION(BlueprintCallable, Category = "Weapon|Multi-Barrel")
    void SetNumberOfBarrels(int32 NewBarrelCount);

    UFUNCTION(BlueprintPure, Category = "Weapon|Multi-Barrel")
    bool IsFireAllBarrelsSimultaneously() const { return bFireAllBarrelsSimultaneously; }

    UFUNCTION(BlueprintCallable, Category = "Weapon|Multi-Barrel")
    void SetFireAllBarrelsSimultaneously(bool bSimultaneous);

    UFUNCTION(BlueprintPure, Category = "Weapon|Multi-Barrel")
    int32 GetCurrentBarrelIndex() const { return CurrentBarrelIndex; }

    UFUNCTION(BlueprintCallable, Category = "Weapon|Multi-Barrel")
    FVector GetBarrelMuzzleLocation(int32 BarrelIndex) const;

    // Rotation functions (works for both turrets and fixed weapons)
    UFUNCTION(BlueprintCallable, Category = "Weapon|Rotation")
    void SetRotationAngle(float RotationAngle, int32 BarrelIndex = -1); // -1 means component rotation for fixed weapons

    UFUNCTION(BlueprintPure, Category = "Weapon|Rotation")
    float GetRotationAngle(int32 BarrelIndex = -1) const; // -1 means component rotation for fixed weapons

    UFUNCTION(BlueprintCallable, Category = "Weapon|Rotation")
    void ResetRotations();

    UFUNCTION(BlueprintPure, Category = "Weapon|Barrel Sockets")
    bool HasBarrelSocket(int32 BarrelIndex) const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Turret")
    UStaticMeshComponent* GetTurretMeshComponent() const;

    UFUNCTION(BlueprintPure, Category = "Weapon|Rotation")
    bool UsesSockets() const { return bIsTurret; }

protected:
    // Internal firing logic
    virtual void PerformShot();
    virtual void SpawnProjectile(const FVector& TargetLocation, const FVector& FireDirection);
    virtual void UpdateFiring(float DeltaTime);
    virtual void UpdateTurretRotation(float DeltaTime);

    // Multi-barrel projectile spawning
    virtual void SpawnProjectileFromBarrel(int32 BarrelIndex, const FVector& TargetLocation, const FVector& FireDirection);

    // Rotation animation functions
    virtual void InitializeRotations();
    virtual void UpdateRotationAnimations(float DeltaTime);
    virtual void TriggerRecoil(int32 BarrelIndex = -1); // -1 means component recoil for fixed weapons
    virtual void SetBarrelRotation(int32 BarrelIndex, float Rotation);
    virtual void SetComponentRotation(float Rotation);
    virtual FName GetBarrelSocketName(int32 BarrelIndex) const;

    // Timing variables
    float LastFireTime = 0.0f;
    float ReloadStartTime = 0.0f;
    float LastBurstShotTime = 0.0f;
    int32 CurrentBurstShot = 0;

    // Turret variables
    float TargetTurretRotation = 0.0f;
    bool bIsRotatingToTarget = false;

    // Multi-barrel variables
    float LastAlternatingFireTime = 0.0f;

    // Barrel animation variables (for turrets)
    TArray<float> BarrelCurrentRotations;
    TArray<float> BarrelTargetRotations;
    TArray<float> BarrelRecoilStartTimes;

    // Component rotation variables (for fixed weapons)
    float ComponentCurrentRotation = 0.0f;
    float ComponentTargetRotation = 0.0f;
    float ComponentRecoilStartTime = -1.0f;

protected:
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon")
    void OnWeaponFiredEvent(const FVector& MuzzleLocation, const FVector& TargetLocation);

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon")
    void OnReloadStarted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon")
    void OnReloadCompleted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon")
    void OnTargetAcquired(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon")
    void OnTargetLost(ARTSBaseActor* Target);

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnWeaponFired, URTSWeaponComponent*, Weapon, const FVector&, MuzzleLocation, const FVector&, TargetLocation);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTargetChanged, URTSWeaponComponent*, Weapon, ARTSBaseActor*, NewTarget);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAmmoChanged, URTSWeaponComponent*, Weapon, int32, NewAmmoCount);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWeaponFired OnWeaponFired;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTargetChanged OnTargetChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAmmoChanged OnAmmoChanged;
};
